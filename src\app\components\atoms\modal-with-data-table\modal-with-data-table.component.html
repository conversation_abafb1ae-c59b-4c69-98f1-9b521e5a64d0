<nb-card>
  <nb-card-header>
    <div class="flex flex-wrap -mx-2">
      <h5 class="w-6/12 px-2">
        <div class="title leading-[40px]">{{ title }}</div>
      </h5>

      <div class="w-6/12 px-2">
        <div class="popup-close float-right">
          <button ghost nbButton (click)="onClose()">
            <nb-icon icon="close"></nb-icon>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>

  <nb-card-body>
    <p-skeleton *ngIf="!data"></p-skeleton>

    <ng-container *ngIf="data">
      <div class="flex flex-wrap -mx-2">
        <div class="px-2 sm:w-full px-2 my-[15px] items-rows">
          <div class="search-filter">
            <nb-form-field>
              <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
              <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
            </nb-form-field>
          </div>
        </div>
      </div>
      <div class="popup-body">
        <div class="w-full px-2 my-[15px]">
          <!-- <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert> -->
        </div>
        <p-table
          #dt
          [filterDelay]="700"
          [value]="data"
          [lazy]="true"
          [paginator]="true"
          [rows]="10"
          [scrollable]="true"
          [scrollHeight]="'500px'"
          selectionMode="single"
          [(selection)]="selectedItem"
          [rowsPerPageOptions]="[5, 10, 20]"
          [totalRecords]="totalRecords"
          [showCurrentPageReport]="true"
          [loading]="loading"
          currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
          dataKey="externalId"
          (onLazyLoad)="nextPage($event)"
        >
          <ng-template pTemplate="header">
            <tr>
              <th *ngFor="let col of columns">
                {{ col.header }}
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-data>
            <tr [pSelectableRow]="data">
              <td>{{ data?.accountNumber }}</td>
              <td>{{ data?.title }}</td>
              <td>{{ data?.primaryBorrowerFullName }}</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </ng-container>
  </nb-card-body>
  <nb-card-footer>
    <div class="w-full px-2">
      <button
        class="float-right"
        [nbSpinner]="loadingSave"
        nbButton
        status="primary"
        style="min-width: 135px"
        (click)="onSave(selectedItem)"
      >
        SELECT
      </button>
    </div>
  </nb-card-footer>
</nb-card>
