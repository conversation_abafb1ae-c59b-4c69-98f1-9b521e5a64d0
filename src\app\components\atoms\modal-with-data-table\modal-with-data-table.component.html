<nb-card>
  <nb-card-header>
    <div class="flex flex-wrap -mx-2">
      <h5 class="w-6/12 px-2">
        <div class="title leading-[40px]">{{ title }}</div>
      </h5>

      <div class="w-6/12 px-2">
        <div class="popup-close float-right">
          <button ghost nbButton (click)="onClose()">
            <nb-icon icon="close"></nb-icon>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>

  <div class="flex-1 p-[20px]">
    <ng-container *ngIf="data">
      <div class="flex flex-wrap -mx-2">
        <div class="px-2 sm:w-full px-2 my-[15px] items-rows">
          <div class="search-filter">
            <nb-form-field>
              <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
              <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
            </nb-form-field>
          </div>
        </div>
      </div>
      <div class="popup-body" #body>
        <p-table
          #dt
          [filterDelay]="700"
          [columns]="columns"
          [value]="data"
          [lazy]="true"
          [paginator]="true"
          [rows]="10"
          [scrollable]="true"
          scrollWidth="flex"
          scrollHeight="flex"
          selectionMode="single"
          [(selection)]="selectedItem"
          [rowsPerPageOptions]="[5, 10, 20]"
          [totalRecords]="totalRecords"
          [showCurrentPageReport]="true"
          [loading]="loading"
          currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
          dataKey="externalId"
          (onLazyLoad)="nextPage($event)"
          [paginatorDropdownAppendTo]="body"
        >
          <ng-template pTemplate="header">
            <tr>
              <th *ngFor="let col of columns">
                {{ col.header }}
              </th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-data>
            <tr [pSelectableRow]="data">
              <td>{{ data?.accountNumber }}</td>
              <td>{{ data?.title }}</td>
              <td>{{ data?.primaryBorrowerFullName }}</td>
              <td>{{ data?.totalOpportunity | currency: "USD" : "symbol" : "2.0" }}</td>
              <td>{{ data?.investmentReturn | number: "1.2-2" }}%</td>
              <td>{{ data?.assetType }}</td>
            </tr>
          </ng-template>

          <ng-template pTemplate="emptymessage">
            <tr>
              <td class="text-center">We couldn't find any investments that match your search criteria</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </ng-container>
  </div>
  <nb-card-footer>
    <div class="w-full px-2">
      <button class="float-right" nbButton status="primary" style="min-width: 135px" (click)="onSave(selectedItem)">
        SELECT
      </button>
    </div>
  </nb-card-footer>
</nb-card>
