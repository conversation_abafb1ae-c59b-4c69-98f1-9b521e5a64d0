import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { BaseComponent } from '@core/models/base.component';
import { InvestmentsLookupByTMOLoanResponse } from '@core/models/response/investments.response';
import { NbButtonModule, NbDialogService, NbIconModule, NbInputModule, NbListModule } from '@nebular/theme';
import { Observable, of, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-input-with-advanced-search',
  imports: [CommonModule, ReactiveFormsModule, NbInputModule, NbButtonModule, NbIconModule, NbListModule],
  templateUrl: './input-with-advanced-search.component.html',
  styleUrl: './input-with-advanced-search.component.scss',
})
export class InputWithAdvancedSearchComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() placeholder = 'Enter TMO Loan Id...';
  @Input() disabled = false;
  @Input() searchResults: InvestmentsLookupByTMOLoanResponse[] = [];
  @Input() isLoading = false;
  @Input() showAdvancedSearchButton = true;
  @Input() modalComponent: any;
  @Input() modalConfig: any = { context: {}, autoFocus: false };

  @Output() selectionChange = new EventEmitter<InvestmentsLookupByTMOLoanResponse | null>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() advancedSearchClick = new EventEmitter<void>();

  @ViewChild('searchInput') searchInput!: ElementRef<HTMLInputElement>;

  public searchDebounceTime = new Subject<string>();

  selectedItem: InvestmentsLookupByTMOLoanResponse | null = null;
  showDropdown = false;
  hasSearched = false;
  dialogService = inject(NbDialogService);

  ngOnInit(): void {
    this.setupSearch();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Handle search results changes from parent
    if (changes['searchResults']) {
      this.handleSearchResults();
    }

    // Handle loading state changes from parent
    if (changes['isLoading']) {
      this.handleLoadingState();
    }
  }

  private handleSearchResults(): void {
    // Show dropdown if user has searched (even with 0 results) or if there are results
    this.showDropdown = this.hasSearched || this.searchResults.length > 0;
  }

  private handleLoadingState(): void {
    // Update dropdown visibility based on loading state and results
    if (!this.isLoading && this.hasSearched) {
      // Show dropdown when not loading and user has searched (even with 0 results)
      this.showDropdown = true;
    }
  }

  eventChange(value: string) {
    this.searchDebounceTime.next(value);
  }

  private setupSearch(): void {
    this.searchDebounceTime
      .pipe(
        debounceTime(300), // Wait 300ms after user stops typing
        distinctUntilChanged(), // Only emit when value actually changes
        switchMap((searchTerm: string | null): Observable<string> => {
          // If an item is selected and user hasn't cleared it, don't search
          if (this.selectedItem && searchTerm === this.selectedItem.accountNumber) {
            return of('');
          }

          // Clear selection if user starts typing something different
          if (this.selectedItem && searchTerm !== this.selectedItem.accountNumber) {
            this.selectedItem = null;
            this.selectionChange.emit(null);
          }

          if (!searchTerm || searchTerm.trim().length < 2) {
            this.showDropdown = false;
            this.hasSearched = false;
            this.searchChange.emit(searchTerm || '');
            return of('');
          }

          this.hasSearched = true;
          const trimmedTerm = searchTerm.trim();
          this.searchChange.emit(trimmedTerm);

          return of(trimmedTerm);
        }),
        takeUntil(this.destroy$), // Prevent memory leaks
      )
      .subscribe();
  }

  onItemSelect(item: InvestmentsLookupByTMOLoanResponse): void {
    this.selectedItem = item;
    this.showDropdown = false;
    this.hasSearched = false;
    // Set the input value to show the selected account number
    this.searchInput.nativeElement.value = item.accountNumber;
    this.selectionChange.emit(item);
  }

  onClearSearch(event?: Event): void {
    // Prevent form submission and event bubbling
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    this.selectedItem = null;
    this.showDropdown = false;
    this.hasSearched = false;
    this.searchDebounceTime.next('');
    this.selectionChange.emit(null);
    this.searchChange.emit('');
    if (this.searchInput) {
      this.searchInput.nativeElement.value = '';
      this.searchInput.nativeElement.focus(); // Focus back to input for new search
    }
  }

  onSearchIconClick(event?: Event): void {
    // Prevent form submission and event bubbling
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Emit event to parent for custom handling
    this.advancedSearchClick.emit();

    // Default behavior: open modal if component is provided
    if (this.showAdvancedSearchButton && this.modalComponent) {
      this.dialogService.open(this.modalComponent, this.modalConfig).onClose.subscribe((data) => {
        if (data) {
          this.onItemSelect(data);
        }
      });
    }
  }

  get showClearIcon(): boolean {
    const inputValue = this.searchInput?.nativeElement?.value || '';
    return !!(this.selectedItem || (inputValue && inputValue.trim().length > 0));
  }

  get showSearchIcon(): boolean {
    return !this.showClearIcon && this.showAdvancedSearchButton;
  }

  get showNoResults(): boolean {
    return this.hasSearched && this.searchResults.length === 0 && !this.isLoading;
  }

  trackByFn(_index: number, item: InvestmentsLookupByTMOLoanResponse): number | string {
    return item.externalId;
  }
}
