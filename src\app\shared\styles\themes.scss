@forward "@nebular/theme/styles/theming";
@use "@nebular/theme/styles/theming" as *;
@use "@nebular/theme/styles/themes/default";
@use "sass:string";

// @import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");
// @import url("https://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300ita‌​lic,400italic,500,500italic,700,700italic,900italic,900");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,500;1,700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Bitter:ital,wght@0,100..900;1,100..900&display=swap");
// @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300&display=swap');

$nb-enable-css-custom-properties: true;

$nb-themes: nb-register-theme(
  (
    // add your variables here like:
    layout-padding: 0px 20px,
    font-family-primary: string.unquote("Lato"),
    font-family-secondary: font-family-primary,
    color-primary-100: #002c24,
    color-primary-200: #002c24,
    color-primary-300: #002c24,
    color-primary-400: #002c24,
    color-primary-500: #002c24,
    color-primary-600: #002c24,
    color-primary-700: #002c24,
    color-primary-800: #002c24,
    color-primary-900: #002c24,
    color-secondary-700: #0f3b33,
    color-lime: #86b502,
    color-grey-700: #576460,
    color-grey-600: #a6b2ae,
    color-yellow-600: #e4e642,
    color-yellow-700: #d9e021,
    color-yellow-800: #c0c71c,
    color-teal-100: #d1f2ef,
    color-teal-300: #6fd4cc,
    color-teal-400: #3cbbb1,
    color-teal-500: #31afa5,
    color-mist: #e7eceb,
    card-text-font-family: font-family-primary,
    text-basic-color: color-primary-700,
    button-filled-primary-background-color: color-primary-500,
    button-filled-primary-border-color: color-primary-500,
    button-filled-text-transform: uppercase,
    button-filled-border-color: color-primary-500,
    button-text-transform: uppercase,
    input-basic-background-color: white,
    input-basic-text-color: color-primary-700,
    input-basic-border-color: color-grey-600,
    select-outline-basic-background-color: white,
    select-outline-basic-text-color: color-primary-700,
    select-outline-basic-border-color: color-grey-600,
    select-outline-basic-placeholder-text-color: color-grey-600,
    select-outline-basic-disabled-text-color: color-grey-600,
    select-outline-basic-disabled-background-color: color-mist,
    select-outline-basic-disabled-border-color: color-mist,
    select-outline-basic-hover-background-color: color-teal-100,
    select-outline-basic-hover-border-color: color-teal-100,
    menu-item-divider-color: transparent,
    card-border-radius: 22px,
    button-text-font-weight: 600,
    button-text-font-size: 15px,
    menu-text-font-weight: 300,
    menu-item-padding: 10px 15px,
    button-filled-basic-background-color: white,
    button-filled-basic-border-color: color-primary-500,
    button-filled-basic-text-color: color-primary-500,
    button-ghost-text-transform: none,
    tabset-tab-text-transform: none,
    tabset-tab-text-font-weight: 400,
    tabset-tab-text-color: color-primary-700,
    tabset-tab-active-text-color: color-lime,
    tabset-tab-hover-text-color: color-lime,
    select-medium-max-width: 50px,
    checkbox-text-font-size: 14px,
    radio-text-font-size: 14px,
    checkbox-padding: 12px 4px,
    button-medium-text-font-size: 14px,
    sidebar-padding: 0px,
    menu-item-icon-width: 36px,
    menu-item-icon-color: color-primary-700,
    menu-text-font-size: 16px,
    input-large-text-font-weight: normal,
    checkbox-text-font-weight: normal,
    radio-text-font-weight: normal,
    select-large-text-font-weight: normal,
    progress-bar-giant-text-font-weight: normal,
    form-field-addon-large-font-weight: normal,
    form-field-addon-medium-font-weight: normal,
    option-large-text-font-weight: normal,
    button-filled-large-padding: 11px 18px,
    button-filled-medium-padding: 11px 18px,
    progress-bar-warning-filled-background-color: rgba(234, 107, 60, 0.2),
    progress-bar-warning-text-color: rgba(234, 107, 60, 1),
    progress-bar-danger-filled-background-color: rgba(235, 87, 87, 0.4),
    progress-bar-danger-text-color: rgba(235, 87, 87, 1),
    progress-bar-border-radius: 12px,
    progress-bar-info-filled-background-color: rgba(195, 225, 242, 0.75),
    progress-bar-info-text-color: rgba(0, 140, 220, 1),
    progress-bar-success-filled-background-color: rgba(1, 184, 170, 0.1),
    progress-bar-success-text-color: #01b8aa,
    form-field-addon-large-width: 100px,
    form-field-addon-medium-width: 100px,
    card-padding: 20px,
    card-border-width: none,
    color-info-default: color-primary-700,
    color-success-default: color-teal-400,
    color-success-hover: color-teal-300,
    color-primary-default: #002c24,
    color-danger-default: #eb5757,
    color-warning-default: color-yellow-700,
    color-warning-hover: color-yellow-600,
    text-heading-6-font-size: 17px,
    // text-info-color: white,
    // header-text-color: white,
    layout-background-color: color-mist,
    button-filled-info-background-color: color-primary-700,
    button-filled-info-border-color: color-primary-700,
    button-filled-info-text-color: white,
    // button-filled-info-hover-background-color: #38B6FF,
    // button-filled-info-hover-border-color: #38B6FF,
    button-filled-basic-hover-background-color: color-mist,
    button-filled-basic-hover-border-color: color-mist,
    button-filled-primary-hover-background-color: color-secondary-700,
    button-filled-primary-hover-border-color: color-secondary-700,
    input-basic-hover-background-color: color-teal-100,
    input-basic-hover-border-color: color-teal-100,
    input-basic-disabled-text-color: color-grey-600,
    input-basic-disabled-background-color: color-mist,
    input-basic-disabled-border-color: color-mist,
    // user-picture-box-border-width: 0px,
    // user-picture-box-border-color: transparent,
    // user-medium-initials-text-font-size: 24px,
    user-initials-text-color: white,
    tabset-tab-active-underline-color: transparent,
    user-small-initials-text-font-size: 10px,
    user-small-initials-text-line-height: 13px,
    user-initials-text-font-weight: 600,
    tabset-divider-width: 0px,
    sidebar-background-color: color-mist,
    tabset-tab-text-font-size: 18px,
    actions-text-color: color-primary-700,
    actions-text-font-size: 16px,
    actions-text-line-height: 24px,
    popover-border-width: 1px,
    popover-border-color: #e7e9ec,
    popover-border-radius: 20px,
    popover-shadow: 0px 0px 20px rgba(0, 0, 0, 0.04),
    list-item-divider-width: 0px,
    list-item-padding: 5px,
    badge-border-radius: 30px,
    user-medium-title-text-font-size: 15px,
    user-medium-title-text-line-height: 25px,
    user-name-text-font-weight: 500,
    chat-background-color: transparent,
    option-list-max-height: 35rem,
    menu-item-active-text-color: color-lime,
    menu-item-icon-active-color: color-lime,
    menu-item-hover-text-color: color-lime,
    menu-group-text-color: color-primary-700,
    sidebar-menu-item-highlight-color: color-lime,
    button-ghost-primary-active-text-color: color-lime,
    button-ghost-primary-active-background-color: transparent,
    button-ghost-primary-hover-text-color: color-lime,
    button-ghost-primary-hover-background-color: transparent,
    button-ghost-primary-focus-text-color: color-secondary-700,
    button-ghost-primary-focus-background-color: transparent,
    button-ghost-primary-focus-border-color: transparent,
    button-ghost-primary-focus-box-shadow: none,
    option-hover-background-color: color-teal-100,
    option-focus-background-color: color-primary-700,
    option-focus-text-color: white
  ),
  default,
  default
);

nb-user.size-medium .user-title {
  color: var(--color-grey-600);
}

nb-badge.position-right {
  right: -9px !important;
}
nb-badge.position-top {
  top: -9px !important;
}

nb-badge {
  height: 22px !important;
  width: 22px !important;
  border: 2px solid white !important;
}

.ng-dirty.ng-invalid {
  border-color: rgba(235, 87, 87, 1) !important;
}

nb-context-menu {
  li.menu-item {
    border-bottom: 1px solid rgba(121, 122, 132, 0.2) !important;
  }

  nb-menu .menu-item a:hover {
    background-color: gray !important;
    color: white !important;
  }

  li.menu-item :hover a {
    color: white !important;
  }

  .menu-group span {
    display: flex;
    align-items: center !important;
  }
}

nb-tag-list.size-medium .nb-tag-list-tags-wrapper {
  margin: 4px !important;
}

nb-progress-bar.status-danger .progress-value {
  border-radius: 12px;
}

nb-theme-default nb-layout-header nav {
  box-shadow: none;
  border-left: 1px solid rgba(44, 51, 73, 0.1);
}

.nb-theme-default nb-select.appearance-outline.size-small .select-button {
  min-width: 100% !important;
}

nb-action {
  border-left: 0 !important;
  padding: 1px 6px !important;
}

.nb-theme-default nb-layout .layout .layout-container nb-sidebar .main-container-fixed {
  // top: 2rem !important;
  position: inherit !important;
}

.nb-theme-default nb-select .select-button {
  min-width: 6rem !important;
  cursor: pointer !important;
}

nb-theme-default .select-button nb-icon {
  right: 11px;
}

nb-user.size-large .user-picture {
  height: 5.25rem !important;
  width: 5.25rem !important;
}

.user-picture.initials {
  border: 2px solid white;
  box-shadow: 0px 0px 3px 1px #dee2e6;
}

.card-pic .user-picture {
  background: #002c24;
  border: 1.5px solid #ffffff;
}

nb-sidebar .main-container {
  transition: width 0.5s;
  transition-delay: 00.1s;
}

nb-sidebar {
  transition: width 0.5s;
}

.appearance-ghost {
  &:focus {
    box-shadow: none;
    color: var(--color-velvet-600);
    &:not(:hover) {
      &:not(:active) {
        box-shadow: none;
        color: var(--color-velvet-600);
      }
    }
  }
}

app-dashboard,
app-investor-details,
app-investment-details,
app-asset-details {
  nb-tabset .tabset .tab a nb-icon {
    width: 30px;
    height: 30px;
  }

  nb-tabset .tabset {
    background: #f7f7f7 !important;
    border-top-left-radius: 12px !important;
    border-top-right-radius: 12px !important;
    // overflow: hidden;
  }

  nb-tabset .tab .tab-link {
    align-items: center;
  }

  nb-tabset .tab.active .tab-link {
    background-color: white !important;
    border-top-left-radius: 12px !important;
    border-top-right-radius: 12px !important;
  }

  nb-tabset .tab.active .tab-link:focus,
  nb-tabset .tab .tab-link:focus {
    outline: none !important;
  }

  .tabset:after,
  .tabset:before {
    content: "" !important;
    display: table !important;
  }
  .tabset:after {
    clear: both !important;
    overflow: hidden !important;
  }
  .tabset li {
    list-style: none outside none !important;
    float: left !important;
    position: relative !important;
  }
  .tabset .active {
    z-index: 3 !important;
  }
  .tabset li:before,
  .tabset li:after,
  .tabset a:before,
  .tabset a:after {
    content: "" !important;
    position: absolute !important;
    bottom: 1px !important;
  }
  .tabset li:before,
  .tabset li:after {
    background: #f7f7f7 !important;
    width: 10px !important;
    height: 10px !important;
  }
  .tabset li:before {
    left: -10px !important;
  }
  .tabset li:after {
    right: -10px !important;
  }
  .tabset a {
    float: left !important;
    padding: 15px 40px !important;
    text-decoration: none !important;
    // color: white !important;
    // background: #be3569 !important;
    border-radius: 12px 12px 0 0 !important;
  }

  .tabset a:before,
  .tabset a:after {
    width: 20px !important;
    height: 20px !important;
    border-radius: 10px !important;
    background: #f7f7f7 !important;
    z-index: 2 !important;
  }
  .tabset a:before {
    left: -20px !important;
  }
  .tabset a:after {
    right: -20px !important;
  }
  .tabset .active:before,
  .tabset .active:after {
    z-index: 1 !important;
    background: white !important;
  }
  .tabset li:first-child a:before
// .tabset li:last-child a:after
  {
    background-color: #fff !important;
  }
}

@theme {
  --color-velvet-600: #0f3b33;
  --color-velvet-700: #002c24;
  --color-mist: #e7eceb;
  --color-grey-700: #576460;
  --color-grey-600: #a6b2ae;
  --color-lime: #86b502;
  --color-yellow-700: #d9e021;
  --color-teal-100: #d1f2ef;
  --color-teal-400: #3cbbb1;

  --font-lato: "Lato", "sans-serif";
  --font-bitter: "Bitter", "serif";
}
