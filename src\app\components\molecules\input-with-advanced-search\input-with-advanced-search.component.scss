.search-container {
    position: relative;
    width: 100%;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;

    .search-input {
        padding-right: 40px; // Make room for icons
        border-radius: 0.75rem 0 0 0.75rem;
        border-right-width: 0;
    }

    .search-icon,
    .clear-icon {
        right: 0px;
        z-index: 10;
        border: 1px solid #a6b2ae;
        border-radius: 0 12px 12px 0;
        color: var(--color-velvet-700);
        cursor: pointer;

        &:hover {
            color: var(--color-lime);
        }
        &:hover,
        &:focus,
        &:active {
            background-color: transparent;
            box-shadow: none;
        }
    }

    .clear-icon {
        position: absolute;
        right: 50px;
        z-index: 10;
        color: var(--color-velvet-700);
        border: none;
        cursor: pointer;
    }

    [nbButton].size-medium.icon-start.icon-end.appearance-ghost {
        padding: 15px;
    }

    .loading-spinner {
        position: absolute;
        z-index: 10;
        color: var(--color-lime);
        right: 50%;
    }
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #edf1f7;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(143, 155, 179, 0.16);
    max-height: 300px;
    overflow-y: auto;
    margin-top: 4px;

    nb-list {
        margin: 0;
        padding: 0;
    }

    .search-result-item {
        cursor: pointer;
        padding: 12px 16px;
        border-bottom: 1px solid #f7f9fc;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: #f7f9fc;
        }

        &:last-child {
            border-bottom: none;
        }

        .result-content {
            .result-title {
                font-weight: 600;
                color: #2e3a59;
                margin-bottom: 4px;
                font-size: 14px;
            }

            .result-details {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                font-size: 12px;
                color: #8f9bb3;

                span {
                    padding: 2px 6px;
                    background-color: #f7f9fc;
                    border-radius: 4px;
                    white-space: nowrap;
                }

                .result-borrower {
                    background-color: #e8f4fd;
                    color: #0088cc;
                }

                .result-type {
                    background-color: #fff3cd;
                    color: #856404;
                }

                .result-asset {
                    background-color: #d1ecf1;
                    color: #0c5460;
                }

                .result-amount {
                    background-color: #d4edda;
                    color: #155724;
                    font-weight: 600;
                }
            }
        }
    }

    .no-results {
        padding: 20px;
        text-align: center;
        color: #8f9bb3;
        font-size: 14px;

        nb-icon {
            margin-right: 8px;
            font-size: 18px;
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .search-dropdown {
        max-height: 250px;
    }

    .search-result-item {
        padding: 10px 12px;

        .result-content {
            .result-title {
                font-size: 13px;
            }

            .result-details {
                font-size: 11px;
                gap: 6px;

                span {
                    padding: 1px 4px;
                }
            }
        }
    }
}

// Dark theme support
:host-context(.nb-theme-dark) {
    .search-dropdown {
        background: #1a1a2e;
        border-color: #342e5c;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);

        .search-result-item {
            border-bottom-color: #342e5c;

            &:hover {
                background-color: #342e5c;
            }

            .result-content {
                .result-title {
                    color: #ffffff;
                }

                .result-details {
                    color: #a4a9b0;

                    span {
                        background-color: #342e5c;
                    }

                    .result-borrower {
                        background-color: #1e3a5f;
                        color: #4dabf7;
                    }

                    .result-type {
                        background-color: #5c4a1a;
                        color: #ffd43b;
                    }

                    .result-asset {
                        background-color: #1a4c5c;
                        color: #3bc9db;
                    }

                    .result-amount {
                        background-color: #2b5a31;
                        color: #51cf66;
                    }
                }
            }
        }

        .no-results {
            color: #a4a9b0;
        }
    }
}
