.search-container {
    position: relative;
    width: 100%;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;

    .search-input {
        padding-right: 40px; // Make room for icons
        border-radius: 0.75rem 0 0 0.75rem;
        border-right-width: 0;
    }

    .search-icon,
    .clear-icon {
        right: 0px;
        z-index: 10;
        border: 1px solid #a6b2ae;
        border-radius: 0 12px 12px 0;
        color: var(--color-velvet-700);
        cursor: pointer;

        &:hover {
            color: var(--color-lime);
        }
        &:hover,
        &:focus,
        &:active {
            background-color: transparent;
            box-shadow: none;
        }
    }

    .clear-icon {
        position: absolute;
        right: 50px;
        z-index: 10;
        color: var(--color-velvet-700);
        border: none;
        cursor: pointer;
    }

    [nbButton].size-medium.icon-start.icon-end.appearance-ghost {
        padding: 15px;
    }

    .loading-spinner {
        position: absolute;
        z-index: 10;
        color: var(--color-lime);
        right: 50%;
    }
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--color-white);
    border: 1px solid var(--color-grey-600);
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(143, 155, 179, 0.16);
    max-height: 300px;
    overflow-y: auto;
    margin-top: 4px;

    nb-list {
        margin: 0;
        padding: 0;
    }

    .search-result-item {
        cursor: pointer;
        padding: 12px 16px;
        border-bottom: 1px solid var(--color-grey-600);
        transition: background-color 0.2s ease;

        &:hover {
            background-color: var(--color-teal-100);
        }

        &:last-child {
            border-bottom: none;
        }

        .result-content {
            .result-title {
                font-weight: 600;
                color: var(--color-velvet-700);
                margin-bottom: 4px;
                font-size: 14px;
            }

            .result-details {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                font-size: 12px;
                color: var(--color-velvet-700);

                span {
                    padding: 2px 6px;
                    background-color: var(--color-mist);
                    color: var(--color-lime);
                    border-radius: 4px;
                    white-space: nowrap;
                }

                .result-borrower {
                    color: var(--color-teal-400);
                }
            }
        }
    }

    .no-results {
        padding: 20px;
        text-align: center;
        color: var(--color-grey-600);
        font-size: 14px;

        nb-icon {
            margin-right: 8px;
            font-size: 18px;
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .search-dropdown {
        max-height: 250px;
    }

    .search-result-item {
        padding: 10px 12px;

        .result-content {
            .result-title {
                font-size: 13px;
            }

            .result-details {
                font-size: 11px;
                gap: 6px;

                span {
                    padding: 1px 4px;
                }
            }
        }
    }
}
