Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDF6110000 ntdll.dll
7FFDF5AC0000 KERNEL32.DLL
7FFDF3A80000 KERNELBASE.dll
7FFDF53D0000 USER32.dll
7FFDF3A50000 win32u.dll
7FFDF4800000 GDI32.dll
7FFDF4010000 gdi32full.dll
7FFDF3850000 msvcp_win.dll
7FFDF3DD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDF5B90000 advapi32.dll
7FFDF4250000 msvcrt.dll
7FFDF42F0000 sechost.dll
7FFDF4650000 RPCRT4.dll
7FFDF37A0000 bcrypt.dll
7FFDF2FF0000 CRYPTBASE.DLL
7FFDF3F80000 bcryptPrimitives.dll
7FFDF51D0000 IMM32.DLL
