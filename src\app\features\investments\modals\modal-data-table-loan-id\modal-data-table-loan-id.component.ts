import { Component, inject, OnInit } from '@angular/core';
import { ModalWithDataTableComponent } from '@components/atoms/modal-with-data-table/modal-with-data-table.component';
import { BaseComponent } from '@core/models/base.component';
import { HttpResponse } from '@core/models/response/http.response';
import { InvestmentsLookupPayload } from '@core/models/response/investments.response';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import { NbDialogRef, NbToastrService } from '@nebular/theme';
import { TableLazyLoadEvent } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-modal-data-table-loan-id',
  imports: [ModalWithDataTableComponent],
  templateUrl: './modal-data-table-loan-id.component.html',
  styleUrl: './modal-data-table-loan-id.component.scss',
})
export class ModalDataTableLoanIdComponent extends BaseComponent implements OnInit {
  title = 'Search investments by TMO Loan ID';
  payload: Filters = {};
  filterParams: Filters = {};
  dtTrigger: Subject<any> = new Subject<any>();
  /**
   * Investment data
   */
  data: any[] = [];

  /**
   * Pagination state
   */
  currentPage = 0;
  pageSize = 10;
  totalRecords = 0;
  loading = false;

  investmentService = inject(InvestmentService);
  investorsService = inject(InvestorsService);
  sharedService = inject(SharedService);
  toast = inject(NbToastrService);
  dialogRef = inject(NbDialogRef<any>);
  entityTypeData: any;

  ngOnInit(): void {
    this.initializeDefaultPayload();
  }

  private initializeDefaultPayload(): void {
    this.filterParams = {
      pageNumber: 0,
      pageSize: 10,
    } as Filters;
  }

  private getInvestmentsLookupByTMOLoan(): void {
    this.investmentService
      .getInvestmentsLookupByTMOLoan(this.filterParams)
      .subscribe((data: HttpResponse<InvestmentsLookupPayload>) => {
        if (data.success) {
          this.data = data.payload.investments;
          this.totalRecords = data.payload.rows;
          this.dtTrigger.next(this.data);
          this.loading = false;
        }
      });
  }

  onSave() {
    console.log('saved');
  }

  onClose() {
    console.log('closed');
    this.dialogRef.close(false);
  }

  nextPage(event: TableLazyLoadEvent) {
    this.loading = true;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.getInvestmentsLookupByTMOLoan();
  }
}
