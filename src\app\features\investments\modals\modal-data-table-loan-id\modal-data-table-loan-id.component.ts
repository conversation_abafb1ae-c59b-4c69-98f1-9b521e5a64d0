import { Component, inject, OnInit } from '@angular/core';
import { ModalWithDataTableComponent } from '@components/atoms/modal-with-data-table/modal-with-data-table.component';
import { BaseComponent } from '@core/models/base.component';
import { TypeKey } from '@core/models/config';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import { NbDialogRef, NbToastrService } from '@nebular/theme';
import { TableLazyLoadEvent } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-modal-data-table-loan-id',
  imports: [ModalWithDataTableComponent],
  templateUrl: './modal-data-table-loan-id.component.html',
  styleUrl: './modal-data-table-loan-id.component.scss',
})
export class ModalDataTableLoanIdComponent extends BaseComponent implements OnInit {
  title = 'TMO Loan ID';
  payload: Filters = {};
  filterParams: Filters = {};
  dtTrigger: Subject<any> = new Subject<any>();
  /**
   * Investment data
   */
  data: any[] = [];

  /**
   * Pagination state
   */
  currentPage = 0;
  pageSize = 10;
  totalRecords = 0;
  loading = false;

  investmentService = inject(InvestmentService);
  investorsService = inject(InvestorsService);
  sharedService = inject(SharedService);
  toast = inject(NbToastrService);
  dialogRef = inject(NbDialogRef<any>);
  entityTypeData: any;

  ngOnInit(): void {
    this.initializeDefaultPayload();

    this.investmentService
      .getEntityType({
        typeKey: TypeKey.EntityType,
      })
      .subscribe((userData: any) => {
        if (userData.success) {
          this.entityTypeData = userData.payload;
        }
      });
  }

  private initializeDefaultPayload(): void {
    this.filterParams = {
      pageNumber: 1,
      pageSize: 10,
      userTypeId: 1, // Client users only
    } as Filters;
  }

  private getList(): void {
    this.investorsService.getInvestors(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.data = (data as any).payload.investors;
        this.totalRecords = (data as any).payload.rows;
        this.dtTrigger.next(this.data);
        this.loading = false;
      }
    });
  }

  onSave() {
    console.log('saved');
  }

  onClose() {
    console.log('closed');
    this.dialogRef.close(false);
  }

  nextPage(event: TableLazyLoadEvent) {
    this.loading = true;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.getList();
  }
}
