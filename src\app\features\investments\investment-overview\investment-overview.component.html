<form class="w-full px-2 my-[15px]" [formGroup]="overviewForm" (ngSubmit)="onSubmit()">
  <!-- <div class="w-full px-2 my-[15px]">
        <nb-alert *ngIf="error" accent="danger">{{error}}</nb-alert>
    </div> -->

  <div class="flex flex-wrap -mx-2 my-[15px]" *ngIf="isInvestor()">
    <div class="w-full px-2">
      <h5 style="margin: 5.5px; font-size: 25px">Overview</h5>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2 my-[15px] align-items-center" *ngIf="isAdmin()">
    <div class="sm:w-6/12 w-full px-2">
      <app-input-with-advanced-search
        #tmoSearchInput
        [placeholder]="'Search TMO Loan ID...'"
        [disabled]="isSearchDisabled"
        [searchResults]="searchResults"
        [isLoading]="isLoading"
        [showAdvancedSearchButton]="true"
        [modalComponent]="customModalComponent"
        [prefilledValue]="existingLoanNumber"
        (selectionChange)="onSelectionChange($event)"
        (searchChange)="onSearchChange($event)"
      ></app-input-with-advanced-search>
    </div>
  </div>
  <div class="flex flex-wrap -mx-2 my-[15px]">
    <div class="sm:w-6/12 px-2 w-full px-2">
      <!-- <div *ngIf="submitted && f.entityName.errors" class="invalid-feedback">
                <div *ngIf="f.entityName.errors.required">Overview is required.</div>
            </div> -->
      <div *ngIf="isAdmin()" class="flex flex-wrap -mx-2 my-[15px]">
        <div class="w-full px-2">
          <h5 style="margin: 5.5px 0px; font-size: 25px">Overview</h5>
        </div>
      </div>

      <div *ngIf="editMode">
        <p *ngIf="!investmentId" class="my-[15px]">
          Note : The file upload feature will be enabled after the initial save
        </p>
        <app-ckeditor variant="full" formControlName="overview"></app-ckeditor>
        <!-- <jodit-editor #editor formControlName="overview" [config]="editorConfig"></jodit-editor> -->
      </div>
      <div
        *ngIf="!editMode"
        class="html-contant w-full px-2 editor-content"
        [innerHTML]="sanitizer.bypassSecurityTrustHtml(editorData)"
      ></div>
    </div>

    <div class="sm:w-6/12 px-2 w-full px-2">
      <div class="flex flex-wrap -mx-2 my-[15px]" *ngIf="isAdmin()">
        <div class="w-full px-2">
          <button
            *ngIf="templateId < 6"
            style="background: #e7eceb"
            class="float-right"
            shape="round"
            status="primary"
            type="button"
            nbButton
            ghost
            (click)="templateId = templateId + 1"
          >
            <nb-icon icon="arrow-ios-forward-outline"></nb-icon>
          </button>
          &nbsp;
          <button
            *ngIf="templateId > 1"
            style="background: #e7eceb; margin-right: 12px"
            class="float-right"
            shape="round"
            status="primary"
            type="button"
            nbButton
            ghost
            (click)="templateId = templateId - 1"
          >
            <nb-icon icon="arrow-ios-back-outline"></nb-icon>
          </button>
        </div>
      </div>

      <div *ngIf="investmentId && (!images || images.length === 0)">
        <div class="w-full px-2">
          <p-skeleton width="100%" height="360px"></p-skeleton>
          <br />
          <p-skeleton width="100%" height="150px"></p-skeleton>
          <br />
          <p-skeleton width="100%" height="150px"></p-skeleton>
        </div>
      </div>
      <div class="flex flex-wrap -mx-2">
        <nb-progress-bar [@inOutPaneAnimation] style="width: 100%" *ngIf="progress" [value]="progress" status="primary">
          Uploading Images {{ progress }}%
        </nb-progress-bar>
        <div *ngIf="images" class="w-full px-2 images-{{ templateId }}">
          <ng-container>
            <div *ngFor="let image of images; let i = index" class="image-block image-block--{{ i }}">
              <div *ngIf="templateId > i">
                <p-skeleton *ngIf="image.loading" width="100%" height="150px"></p-skeleton>

                <div *ngIf="isAdmin() && !image.data && !image.loading" style="padding: 0px; transition: all 0.5s">
                  <!-- <br> -->
                  <div class="file-container" for="fileDropRef" appDnd (fileDropped)="onFileDropped($event, i)">
                    <input type="hidden" />
                    <input
                      type="file"
                      #fileDropRef
                      [id]="'fileDropRef' + i"
                      (change)="fileBrowseHandler($event, i)"
                      accept="image/png, image/jpeg, image/jpg"
                    />
                    <p class="m-0" for="fileDropRef">
                      <!-- <nb-icon icon="file-add"></nb-icon> -->
                      Drop Image here or Click to upload.
                    </p>
                  </div>
                </div>
                <img
                  *ngIf="image.data"
                  [src]="image.data"
                  alt="Gallery image 1"
                  class="gallery__img--{{ i }}"
                  [loading]="i === 0 ? 'eager' : 'lazy'"
                />
                <div *ngIf="image.data" class="full-icon">
                  <nb-icon
                    *ngIf="isAdmin()"
                    class="clickable"
                    (click)="deleteDocument(image.documentKey, i)"
                    icon="trash-2-outline"
                    nbTooltip="Delete Image"
                    nbTooltipStatus="control"
                    nbTooltipPlacement="bottom"
                  >
                  </nb-icon>

                  <nb-icon
                    class="clickable"
                    (click)="fullImageView(image.data)"
                    icon="expand-outline"
                    nbTooltip="Full View"
                    nbTooltipStatus="control"
                    nbTooltipPlacement="bottom"
                  >
                  </nb-icon>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>

  <div class="w-full px-2 my-[15px] flex justify-end gap-2">
    <button
      class="float-right"
      *ngIf="isAdmin() && investmentId"
      [disabled]="loadingDelete"
      [nbSpinner]="loadingDelete"
      nbButton
      status="danger"
      style="min-width: 135px"
      (click)="onDeleteInvestment($event)"
    >
      DELETE
    </button>
    <button
      class="float-right"
      *ngIf="isAdmin()"
      [disabled]="loading"
      [nbSpinner]="loading"
      nbButton
      status="primary"
      style="min-width: 135px"
    >
      <div *ngIf="!investmentId; then saveText; else updateText"></div>
      <ng-template #saveText> SAVE </ng-template>
      <ng-template #updateText> UPDATE </ng-template>
    </button>

    <span class="hide-on-mobile" *ngIf="isInvestor()">
      <button
        class="hide-on-mobile float-right"
        *ngIf="isInvestor()"
        (click)="changeTab.emit(true)"
        [nbSpinner]="loading"
        nbButton
        status="primary"
        style="min-width: 135px"
      >
        NEXT
      </button>
    </span>
  </div>

  <!-- [disabled]="!userForm.valid" -->
</form>

<p-dialog header="   " [(visible)]="display">
  <div style="max-width: 900px">
    <img [src]="fullImageSrc" />
  </div>
</p-dialog>
