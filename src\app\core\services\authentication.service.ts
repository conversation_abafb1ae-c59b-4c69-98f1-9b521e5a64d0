import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { RegisterUser } from '@core/models/auth';
import { HttpResponse } from '@core/models/response/http.response';
import { ExternalLoginResponse, UserParamsResponse } from '@core/models/response/user-data.response';
import { NbAclService } from '@nebular/security';
import { NbToastrService } from '@nebular/theme';
import jwt_decode from 'jwt-decode';
import { BehaviorSubject, EMPTY, Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { InvestorsService } from './investors.service';

export enum TypeLoginEnum {
  RenewToken = 0,
  Google = 1,
  Microsoft = 2,
}

@Injectable({ providedIn: 'root' })
export class AuthenticationService {
  private userSubject: BehaviorSubject<any>;
  public user: Observable<any>;

  constructor(
    private router: Router,
    private http: HttpClient,
    private nbAclService: NbAclService,
    private toastr: NbToastrService,
    private investorsService: InvestorsService,
  ) {
    this.userSubject = new BehaviorSubject<any>(JSON.parse(localStorage.getItem('user') as string));
    this.user = this.userSubject.asObservable();
  }

  public get userValue(): any {
    return this.userSubject.value;
  }

  switchOrg(orgKey: string, investorId: number): any {
    return this.http.post<any>(`${environment.apiURL}/Identity/SwitchOrg`, {
      orgKey,
      investorId,
      host: window.location.host,
    });
  }

  externalLogin(token: string, typeLogin: TypeLoginEnum): any {
    return this.http
      .post<HttpResponse<ExternalLoginResponse>>(`${environment.apiURL}/Identity/ExternalLogin`, {
        token: token,
        typeLogin,
      })
      .pipe(
        map((userResponse) => {
          if (userResponse.success && userResponse.payload.isSuccess) {
            userResponse.payload.isLoggedIn = true;
            userResponse.payload.isAdmin = false;
            if (userResponse.payload.orgList) {
              userResponse.payload.role = userResponse.payload.orgList[0].roles[0].displayName.split(' ').join('');
            }
            localStorage.setItem('user', JSON.stringify(userResponse.payload));
            this.userSubject.next(userResponse.payload);
            this.setPermissions();
            return userResponse;
          } else {
            // Handle unsuccessful login response
            const errorMessage = userResponse.error.message;
            this.toastr.danger(errorMessage, 'Error!', {
              duration: 4000,
              toastClass: 'toastr-width',
            });
            // Throw error to trigger catchError
            throw new Error(errorMessage);
          }
        }),
        catchError((error) => {
          // Return EMPTY to complete the observable without emitting
          return EMPTY;
        }),
      );
  }

  registerWithProfile(registerData: RegisterUser): any {
    return this.http.post<any>(`${environment.apiURL}/Identity/RegisterWithProfile`, registerData).pipe(
      map((response) => {
        return response;
      }),
    );
  }

  saveToken(userResponse: any): void {
    if (userResponse.success && userResponse.payload.isSuccess) {
      userResponse.payload.isLoggedIn = true;
      userResponse.payload.isAdmin = true;
      userResponse.payload.role = userResponse.payload.orgList[0].roles[0].displayName.split(' ').join('');
      localStorage.setItem('user', JSON.stringify(userResponse.payload));
      this.setPermissions();
      this.userSubject.next(userResponse.payload);
    } else {
      this.logout();
    }
  }

  setPermissions(): void {
    if (this.userValue && this.userValue.orgList && this.userValue.orgList.length > 0) {
      const role = this.userValue.orgList[0].roles[0].displayName.split(' ').join('');
      const permissions = this.userValue.permissions;
      console.log('🚀 ~ AuthenticationService ~ setPermissions ~ permissions:', permissions);
      if (permissions) {
        const grouped = this.groupBy(permissions, (permission: any) => permission.functionName);
        this.nbAclService.setAccessControl({ [role]: grouped as any });
      }
    }
  }

  authCheck(): any {
    return this.http.get<any>(`${environment.apiURL}/api/AuthCheck`).pipe(
      map((response) => {
        return response;
      }),
    );
  }

  getUserParams(token: string): any {
    return this.http
      .post<HttpResponse<UserParamsResponse>>(`${environment.apiURL}/identity/get-userparams`, { token })
      .pipe(
        map((response) => {
          return response;
        }),
      );
  }

  refreshAccessToken(): any {
    return this.http
      .post<any>(`${environment.apiURL}/Identity/RenewToken`, {
        username: this.getEmailFromToken(),
        token: this.userValue.renewToken,
        typeLogin: TypeLoginEnum.RenewToken,
      })
      .pipe(
        map((response) => {
          return response;
        }),
      );
  }

  getEmailFromToken(): string {
    const decodedToken: any = jwt_decode(this.userValue?.token); // decode token
    return decodedToken.UserNameClaimToken;
  }

  logout(): void {
    // remove user from local storage to log user out
    localStorage.removeItem('user');
    localStorage.removeItem('form');
    localStorage.removeItem('account');
    localStorage.removeItem('urlToken');
    this.userSubject.next(null);
    this.investorsService.accountSubject.next(null);
    this.router.navigate(['/login']);
  }

  private groupBy(list: any, keyGetter: any): any {
    const mapObj = new Map();
    list.forEach((item: any) => {
      const key = keyGetter(item);
      const collection = mapObj.get(key);
      if (!collection) {
        mapObj.set(key, [item.refName]);
      } else {
        collection.push(item.refName);
      }
    });
    return this.strMapToObj([...mapObj]);
  }

  private strMapToObj(strMap: any): any {
    const obj = Object.create(null);
    for (const [k, v] of strMap) {
      // We don’t escape the key '__proto__'
      // which can cause problems on older engines
      obj[k] = v;
    }
    return obj;
  }
}
