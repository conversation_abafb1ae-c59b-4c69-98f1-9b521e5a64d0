<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <div *ngIf="!investmentId; then saveText; else updateText"></div>
      <ng-template #saveText>
        <h5>New Investment</h5>
      </ng-template>
      <ng-template #updateText>
        <h5>{{ entityName }}</h5>
      </ng-template>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="w-full px-2">
    <nb-card style="box-shadow: 0px 0px 4px 1px #d3d3d36b; border-radius: 12px">
      <nb-card-body style="padding: 0">
        <nb-tabset class="hide-tab-mobile" (changeTab)="tabClick($event)" #tabset>
          <nb-tab
            tabId="1"
            [responsive]="'true'"
            tabTitle="Overview"
            [tabIcon]="{ icon: 'overviewIcon', pack: 'custom' }"
            active
          >
            <div *ngIf="tabId === 1">
              <app-investment-overview
                #overviewComponent
                (investmentChange)="investmentChange($event)"
                (changeTab)="changeTab($event)"
                [investmentDetails]="investmentDetails()"
              >
              </app-investment-overview>
            </div>
          </nb-tab>

          <nb-tab
            tabId="2"
            [disabled]="!investmentId"
            tabTitle="Details"
            [tabIcon]="{ icon: 'financialsIcon', pack: 'custom' }"
          >
            <div *ngIf="tabId === 2">
              <app-investment-financials
                (changeTab)="changeTab($event)"
                [iconsData]="iconsData"
              ></app-investment-financials>
            </div>
          </nb-tab>

          <nb-tab
            tabId="3"
            *ngIf="isAdmin() || isManager()"
            [disabled]="!investmentId"
            tabTitle="Key Data"
            [tabIcon]="{ icon: 'notesIcon', pack: 'custom' }"
          >
            <div *ngIf="tabId === 3">
              <app-investment-key-data
                *ngIf="investmentId"
                [investmentDetails]="investmentDetails()"
                [investmentStateData]="investmentStateData"
                [statusData]="statusData"
              >
              </app-investment-key-data>
            </div>
          </nb-tab>

          <nb-tab
            tabId="4"
            *ngIf="isAdmin() || isManager()"
            [disabled]="!investmentId"
            tabTitle="Documents"
            [tabIcon]="{ icon: 'document30Icon', pack: 'custom' }"
          >
            <div *ngIf="tabId === 4">
              <app-investment-documents (changeTab)="changeTab($event)"></app-investment-documents>
            </div>
          </nb-tab>

          <nb-tab
            tabId="5"
            *ngIf="isInvestor() && showApply"
            [disabled]="!investmentId"
            tabTitle="Apply"
            [tabIcon]="{ icon: 'notesIcon', pack: 'custom' }"
          >
            <div *ngIf="tabId === 5">
              <app-investment-apply *ngIf="investmentId"> </app-investment-apply>
            </div>
          </nb-tab>
        </nb-tabset>

        <nb-accordion class="show-on-mobile">
          <nb-accordion-item>
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="overviewIcon" pack="custom"></nb-icon> Overview
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-investment-overview (investmentChange)="investmentChange($event)" (changeTab)="changeTab($event)">
              </app-investment-overview>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <nb-accordion-item [disabled]="!investmentId">
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="financialsIcon" pack="custom"></nb-icon> Financials
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-investment-financials [investmentId]="investmentId" (changeTab)="changeTab($event)">
              </app-investment-financials>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <nb-accordion-item *ngIf="isAdmin()" [disabled]="!investmentId">
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="notesIcon" pack="custom"></nb-icon> Key Data
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-investment-key-data *ngIf="investmentId"> </app-investment-key-data>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <nb-accordion-item *ngIf="isAdmin()" [disabled]="!investmentId">
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="document30Icon" pack="custom"></nb-icon> Documents
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-investment-documents (changeTab)="changeTab($event)"></app-investment-documents>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <nb-accordion-item *ngIf="isInvestor() && showApply" [disabled]="!investmentId">
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="notesIcon" pack="custom"></nb-icon> Apply
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-investment-apply *ngIf="investmentId"> </app-investment-apply>
            </nb-accordion-item-body>
          </nb-accordion-item>
        </nb-accordion>
      </nb-card-body>
    </nb-card>
  </div>
</div>
