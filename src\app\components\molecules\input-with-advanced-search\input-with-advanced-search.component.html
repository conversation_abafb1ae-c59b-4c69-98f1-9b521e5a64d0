<div class="search-container">
  <!-- Search Input with Icons -->
  <div class="search-input-wrapper">
    <input
      #searchInput
      nbInput
      fullWidth
      shape="semi-round"
      fieldSize="large"
      [placeholder]="placeholder"
      (keyup)="eventChange(searchInput.value)"
      class="search-input"
      autocomplete="off"
    />

    <button
      nbButton
      ghost
      size="medium"
      class="search-icon"
      tabindex="-1"
      type="button"
      (click)="onSearchIconClick($event)"
      style="background: #e7eceb"
      title="Search with popup"
    >
      <nb-icon icon="search-outline" pack="eva"></nb-icon>
    </button>

    <!-- Clear Icon (shown when input has value or item is selected) -->
    <button
      *ngIf="showClearIcon"
      nbButton
      ghost
      size="medium"
      class="clear-icon"
      type="button"
      (click)="onClearSearch($event)"
      title="Clear search"
    >
      <nb-icon icon="close-outline" pack="eva"></nb-icon>
    </button>

    <!-- Loading Spinner -->
    <div *ngIf="isLoading" class="loading-spinner">
      <nb-icon icon="loader-outline" pack="eva" [options]="{ animation: { type: 'pulse' } }"></nb-icon>
    </div>
  </div>

  <!-- Dropdown Results -->
  <div *ngIf="showDropdown" class="search-dropdown">
    <nb-list>
      <nb-list-item
        *ngFor="let item of searchResults; trackBy: trackByFn"
        (click)="onItemSelect(item)"
        class="search-result-item"
      >
        <div class="result-content">
          <div class="result-title">{{ item.title }}</div>
          <div class="result-details">
            <span *ngIf="item.accountNumber" class="result-account-number">{{ item.accountNumber }}</span>
            <span *ngIf="item.primaryBorrowerFullName" class="result-borrower">{{ item.primaryBorrowerFullName }}</span>
          </div>
        </div>
      </nb-list-item>
    </nb-list>

    <!-- No Results Message -->
    <div *ngIf="showNoResults" class="no-results flex align-center justify-center">
      <nb-icon icon="search-outline" pack="eva"></nb-icon>
      <span>No investments found</span>
    </div>
  </div>
</div>
