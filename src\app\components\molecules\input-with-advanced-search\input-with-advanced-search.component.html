<div class="search-container">
  <!-- Search Input with Icons -->
  <div class="search-input-wrapper">
    <input
      #searchInput
      nbInput
      fullWidth
      shape="semi-round"
      fieldSize="large"
      [placeholder]="selectedItem || isPrefilledMode ? '' : placeholder"
      (keyup)="eventChange(searchInput.value)"
      [class]="
        'search-input' +
        (selectedItem || isPrefilledMode ? ' item-selected' : '') +
        (isPrefilledMode ? ' prefilled' : '')
      "
      autocomplete="off"
      [readonly]="!!(selectedItem || isPrefilledMode)"
    />

    <button
      *ngIf="!isPrefilledMode"
      nbButton
      ghost
      size="medium"
      class="search-icon"
      tabindex="-1"
      type="button"
      (click)="onSearchIconClick($event)"
      style="background: #e7eceb"
      title="Search with popup"
    >
      <nb-icon icon="search-outline" pack="eva"></nb-icon>
    </button>

    <!-- Clear Icon (shown when input has value or item is selected) -->
    <button
      *ngIf="showClearIcon"
      nbButton
      ghost
      size="medium"
      class="clear-icon"
      type="button"
      (click)="onClearSearch($event)"
      title="Clear search"
    >
      <nb-icon icon="close-outline" pack="eva"></nb-icon>
    </button>

    <!-- Loading Spinner -->
    <div *ngIf="isLoading" class="loading-spinner">
      <nb-icon icon="loader-outline" pack="eva" [options]="{ animation: { type: 'pulse' } }"></nb-icon>
    </div>
  </div>

  <!-- Dropdown Results -->
  <div *ngIf="showDropdown" class="search-dropdown">
    <nb-list>
      <nb-list-item
        *ngFor="let item of searchResults; trackBy: trackByFn"
        (click)="onItemSelect(item)"
        class="search-result-item cursor-pointer hover:bg-teal-100 transition-colors duration-200 ease-in-out"
      >
        <div class="result-content">
          <div class="font-semibold text-velvet-700 mb-1 text-sm">{{ item.title }}</div>
          <div class="flex flex-wrap gap-2 text-xs">
            <span *ngIf="item.accountNumber" class="px-[6px] py-[2px] bg-mist text-lime rounded whitespace-nowrap">{{
              item.accountNumber
            }}</span>
            <!-- <span *ngIf="item.primaryBorrowerFullName" class="px-[6px] py-[2px] bg-mist text-teal-400 rounded whitespace-nowrap">{{ item.primaryBorrowerFullName }}</span> -->
          </div>
        </div>
      </nb-list-item>
    </nb-list>

    <!-- No Results Message -->
    <div *ngIf="showNoResults" class="no-results flex align-center justify-center text-grey-600 p-[20px]">
      <nb-icon icon="search-outline" pack="eva"></nb-icon>
      <span>No investments found</span>
    </div>
  </div>
</div>
