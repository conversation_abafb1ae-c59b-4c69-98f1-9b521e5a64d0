import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { BaseComponent } from '@core/models/base.component';
import {
  NbAlertModule,
  NbButtonModule,
  NbCardModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbSpinnerModule,
} from '@nebular/theme';
import { SkeletonModule } from 'primeng/skeleton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';

@Component({
  selector: 'app-modal-with-data-table',
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbButtonModule,
    NbAlertModule,
    NbSpinnerModule,
    NbFormFieldModule,
    NbInputModule,
    NbSelectModule,
    TableModule,
    SkeletonModule,
  ],
  standalone: true,
  templateUrl: './modal-with-data-table.component.html',
  styleUrl: './modal-with-data-table.component.scss',
})
export class ModalWithDataTableComponent extends BaseComponent {
  @Input() data: any;
  @Input() dataHeaderDrop: any;
  @Input() title = '';
  @Input() loading = false;
  @Input() error = '';
  @Input() totalRecords = 0; // Total number of records in the data table.
  @Output() eventSave = new EventEmitter<any>(); // Emits the selected item when the user clicks the "Save" button.
  @Output() eventClose = new EventEmitter<boolean>(); // Emits when the user clicks the "Close" button.
  @Output() eventNextPage = new EventEmitter<any>(); // Emits when the user clicks the "Next Page" button.
  @ViewChild('dt') dt: any;
  selectedItem: any;
  columns = [
    { field: 'accountNumber', header: 'Account Number' },
    { field: 'title', header: 'Title' },
    { field: 'primaryBorrowerFullName', header: 'Primary Borrower' },
    { field: 'totalOpportunity', header: 'Total Opportunity' },
    { field: 'investmentReturn', header: 'Investment Return' },
    { field: 'assetType', header: 'Asset Type' },
  ];

  onClose() {
    this.eventClose.emit(true);
  }

  onSave(item: any) {
    this.eventSave.emit(item);
  }

  nextPage(event: TableLazyLoadEvent) {
    this.eventNextPage.emit(event);
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }
}
