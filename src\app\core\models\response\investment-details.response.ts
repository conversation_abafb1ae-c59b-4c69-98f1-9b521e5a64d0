export interface ImageUrl {
  url: string;
  sequence: number;
  documentKey: string;
  attachedBy: number;
}

export interface InvestmentDetailsResponse {
  id?: number;
  overview?: string;
  title?: string;
  borrower?: string;
  totalOpportunity?: number;
  investmentType?: string;
  assetType?: string;
  term?: number;
  investmentReturn?: number;
  lvr?: number;
  minInvestment?: number;
  assetTypeId?: number | null;
  investmentTypeId?: number | null;
  showApply?: boolean;
  startDate?: Date;
  stateId?: number;
  suburb?: string;
  statusId?: number;
  templateId?: number;
  imageUrls?: ImageUrl[];
  loanId?: string; // TMO Loan ID field
  loanNumber?: string;
}

export interface KeyData extends InvestmentDetailsResponse {
  investmentId: number;
  isDeleted?: boolean;
  userId?: number;
}

export interface InvestmentDetailsTMOLoanResponse extends InvestmentDetailsResponse {
  externalId?: string;
  accountNumber?: string;
  propertyState?: string;
  categories?: string;
  dateModified?: string;
  notes?: string | null;
  bookingDate?: string;
  primaryBorrowerFullName?: string;
  primaryBorrowerRecID?: string;
}
