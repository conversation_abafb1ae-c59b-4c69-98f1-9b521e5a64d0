import { animate, style, transition, trigger } from '@angular/animations';
import { CommonModule } from '@angular/common';
import { HttpEventType } from '@angular/common/http';
import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { CkeditorComponent } from '@components/atoms/ckeditor/ckeditor.component';
import { InputWithAdvancedSearchComponent } from '@components/molecules/input-with-advanced-search/input-with-advanced-search.component';
import { DeleteDocumentComponent } from '@components/templates/delete-document/delete-document.component';
import { ImageHelper } from '@core/helpers/image.helper';
import { DocumentType } from '@core/models/config';
import { SearchParam } from '@core/models/general';
import { HttpResponse } from '@core/models/response/http.response';
import {
  InvestmentDetailsResponse,
  InvestmentDetailsTMOLoanResponse,
} from '@core/models/response/investment-details.response';
import {
  InvestmentsLookupByTMOLoanResponse,
  InvestmentsLookupPayload,
} from '@core/models/response/investments.response';
import { AuthenticationService } from '@core/services/authentication.service';
import { DocumentService } from '@core/services/document.service';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbAlertModule,
  NbButtonModule,
  NbDialogService,
  NbIconModule,
  NbProgressBarModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { DialogModule } from 'primeng/dialog';
import { SkeletonModule } from 'primeng/skeleton';
import { Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ModalDataTableLoanIdComponent } from '../modals/modal-data-table-loan-id/modal-data-table-loan-id.component';
declare let window: any;

@Component({
  selector: 'app-investment-overview',
  templateUrl: './investment-overview.component.html',
  styleUrls: ['./investment-overview.component.scss'],
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({ transform: 'translateY(-100%)' }),
        animate('500ms ease-in', style({ transform: 'translateY(0%)' })),
      ]),
      transition(':leave', [animate('500ms ease-in', style({ transform: 'translateY(-100%)' }))]),
    ]),
    trigger('FadeInAnimation', [
      // The '* => *' will trigger the animation to change between any two states
      transition(':enter', [style({ opacity: 0 }), animate('1s', style({ opacity: 1 }))]),
      transition(':leave', [style({ opacity: 1 }), animate('1s', style({ opacity: 0 }))]),
    ]),
    trigger('inOutPaneAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(+100%)' }), // apply default styles before animation starts
        animate('750ms ease-in-out', style({ opacity: 1, transform: 'translateX(0)' })),
      ]),
      transition(':leave', [
        style({ opacity: 1, transform: 'translateX(0)' }), // apply default styles before animation starts
        animate('600ms ease-in-out', style({ opacity: 0, transform: 'translateX(+100%)' })),
      ]),
    ]),
  ],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbIconModule,
    NbButtonModule,
    NbAlertModule,
    NbProgressBarModule,
    NbSpinnerModule,
    SkeletonModule,
    DialogModule,
    CkeditorComponent,
    InputWithAdvancedSearchComponent,
  ],
})
export class InvestmentOverviewComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() investmentDetails: InvestmentDetailsResponse | null = null;
  @ViewChild('editor') editor: any;

  @ViewChild('fileDropRef', { static: false }) fileDropRef!: ElementRef;

  @Output() investmentChange = new EventEmitter<InvestmentDetailsResponse>();
  @Output() changeTab = new EventEmitter<boolean>();

  editMode = false;
  overviewForm: UntypedFormGroup;
  loading = false;
  submitted = false;
  returnUrl = '';
  error = '';
  title = 'Edit Client';
  investments!: any;
  display = false;

  templateId: any = 1;
  investmentId!: number;
  images: any = [];

  imagesCount = [1, 2, 3, 4, 5, 5];

  public editorConfig: any = {};

  public editorData = '';
  fullImageSrc: any;
  investorId: number | undefined;
  progress = 0;
  uploadedDocuments: any;
  detailsTMOSelected: InvestmentDetailsTMOLoanResponse | null = null;
  isSearchDisabled = false;
  searchResults: InvestmentsLookupByTMOLoanResponse[] = [];
  isLoading = false;
  customModalComponent = ModalDataTableLoanIdComponent; // Your custom modal component

  constructor(
    private formBuilder: UntypedFormBuilder,
    private documentService: DocumentService,
    private route: ActivatedRoute,
    private router: Router,
    public toastr: NbToastrService,
    private sharedService: SharedService,
    private dom: DomSanitizer,
    private investorsService: InvestorsService,
    private investmentService: InvestmentService,
    private dialogService: NbDialogService,
    private authenticationService: AuthenticationService,
    public sanitizer: DomSanitizer,
  ) {
    this.overviewForm = this.formBuilder.group({
      overview: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {
    this.investmentId = this.sharedService.getFormParamValue.investmentId || 0;
    this.returnUrl = this.route.snapshot.queryParams.returnUrl || '/';

    this.setConfig();

    if (this.isAdmin()) {
      this.editMode = true;
    }

    // Handle initial data if available
    if (this.investmentDetails) {
      this.loadInvestmentData(this.investmentDetails);
    } else {
      // Fallback to shared service for investment ID
      this.investmentId = this.sharedService.getFormParamValue.investmentId || 0;
      if (!this.investmentId) {
        this.initializeEmptyImages();
      }
    }

    setTimeout(() => {
      this.editorConfig.removeButtons = ['file'];
    }, 5000);
  }

  private setConfig(): void {
    this.editorConfig = {
      toolbar: true,
      minHeight: 500,
      toolbarButtonSize: 'middle',
      removeButtons: this.investmentId ? ['fullsize', 'print', 'about'] : ['fullsize', 'print', 'about', 'file'],
      toolbarAdaptive: false,
      uploader: {
        enableDragAndDropFileToEditor: true,
        url: `${environment.apiURL}/api/Document/save-document`,
        headers: {
          WorkjetAppToken: environment.workjetAppToken,
          Authorization: `Bearer ${this.authenticationService.userValue.token}`,
        },
        insertImageAsBase64URI: true,
        data: {
          generateProxyUrl: true,
          isInvestment: true,
          investmentId: this.investmentId,
        },
        prepareData(data: any): any {
          return data;
        },
        isSuccess(resp: any): any {
          return !resp.error;
        },
        getMsg(resp: any): any {
          return resp.msg.join !== undefined ? resp.msg.join(' ') : resp.msg;
        },
        process(resp: any): any {
          return resp;
        },
        error: (e: any) => {
          console.log('error', e);
        },
        defaultHandlerSuccess(imageData: any): any {
          // eslint-disable-next-line @typescript-eslint/no-this-alias
          const editor: any = this;
          if (imageData.payload) {
            const docUrl = imageData.payload.imageUrls[0];
            if (docUrl) {
              const element = `<span style="cursor:pointer;color:#002c24" onclick="downloadDoc('${imageData.payload.saveFileResult[0]?.documentKey}','${imageData.payload.uploadedBy}')">${imageData.payload.saveFileResult[0].fileName}</span>`;
              editor.s.insertHTML('<br>');
              editor.s.insertHTML(element);
              editor.s.insertHTML('<br>');
            }
          } else {
            editor.s.insertHTML('<br>');
            editor.s.insertImage(imageData.files[0]);
            editor.s.insertHTML('<br>');
          }
        },
        defaultHandlerError: (resp: any) => {
          console.log('defaultHandlerError', resp);
        },
      },
    };
  }

  ngAfterViewInit(): void {
    window.overviewCompoenent = this;
    window.downloadDoc = (docKey: any, uploadedBy: any) => {
      if (docKey) {
        window.overviewCompoenent.toastr.default(`Downloading started`, '', {
          icon: 'download',
        });

        window.overviewCompoenent.documentService.getDocumentFile({
          documentKey: docKey,
          userId: uploadedBy,
        });
      } else {
        window.overviewCompoenent.toastr.error('', 'Invalid download request.');
      }
    };
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Handle updates when parent passes new data
    if (changes['investmentDetails'] && changes['investmentDetails'].currentValue) {
      const details = changes['investmentDetails'].currentValue;
      this.loadInvestmentData(details);
    }
  }

  // private getInvestmentDetail(): void {
  //   this.investmentService
  //     .getInvestmentDetail(this.investmentId, this.investorId ? this.investorId : undefined)
  //     .subscribe(async (data: any) => {
  //       if (data.success) {
  //         this.editorData = data.payload.overview;
  //         this.overviewForm.patchValue({
  //           overview: this.editorData,
  //         });

  //         this.templateId = data.payload.templateId;

  //         // data.payload.imageUrls.forEach(async (element: any, index: number) => {
  //         //   if (!this.images[index]) {
  //         //     this.images[index] = {};
  //         //   }
  //         //   this.images[index] = ({ data: await this.getImage(element.url), });
  //         // });

  //         for (const index in this.imagesCount) {
  //           if (index) {
  //             if (this.imagesCount.length - 1 === Number(index)) {
  //               return;
  //             }

  //             if (!this.images[index]) {
  //               this.images[index] = { loading: true };
  //             }

  //             const [imageData] = data.payload.imageUrls.filter((t: any) => Number(t.sequence) === Number(index) + 1);
  //             if (imageData) {
  //               this.images[index] = {
  //                 loading: false,
  //                 data: imageData.url || ImageHelper.noImage(),
  //                 documentKey: imageData.documentKey,
  //               };
  //             } else {
  //               this.images[index] = { loading: false, data: null };
  //             }
  //           }
  //         }
  //         this.investments = data.payload;
  //         this.investmentChange.emit(this.investments);
  //       }
  //     });
  // }

  get f() {
    return this.overviewForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    // stop here if form is invalid
    if (this.overviewForm.invalid) {
      return;
    }

    this.loading = true;

    const formData = new FormData();

    formData.append('Overview', this.overviewForm.value.overview);

    if (this.investmentId) {
      formData.append('Id', this.investmentId as any);
    }

    if (this.detailsTMOSelected && this.detailsTMOSelected.externalId) {
      formData.append('LoanExternalId', this.detailsTMOSelected.externalId);
    }

    formData.append('TemplateId', this.templateId);

    this.investmentService.saveOverview(formData).subscribe(
      (response: any) => {
        if (response.success) {
          this.submitted = false;
          const isNewInvestment = !this.investmentId;
          if (isNewInvestment) {
            this.investmentId = response.payload.investmentId;
            this.setConfig();
            this.sharedService.setFormParamValue({
              investmentId: this.investmentId,
              changeTab: true,
            });
            this.onClearTMOSelection();
          }

          // if (this.images) {
          //   this.images.forEach((element: any, index: number) => {
          //     if (element.files) {
          //       this.prepareFilesList(element.files, index + 1);
          //     }
          //   });
          // }

          // this.loading = false;
          // this.toastr.success('Saved successfully!', 'Success');
          // window.scrollTo({
          //   top: 0,
          //   left: 0,
          //   behavior: 'smooth',
          // });
          // Check if there are images to upload
          const imagesToUpload = this.images.filter((element: any) => element.files);

          if (imagesToUpload.length > 0) {
            // Upload images and wait for completion
            this.uploadImagesAndNotify(imagesToUpload, response.payload, isNewInvestment);
          } else {
            // No images to upload, emit immediately
            this.completeSubmission(response.payload, isNewInvestment);
          }
        } else {
          this.toastr.danger(response.error.message, 'Error!');
          this.loading = false;
          this.submitted = false;
        }
      },
      (err: any) => {
        this.toastr.danger(err.error.message, 'Error!');
        this.loading = false;
        this.submitted = false;
      },
    );
  }

  private uploadImagesAndNotify(imagesToUpload: any[], responsePayload: any, isNewInvestment: boolean): void {
    let completedUploads = 0;
    const totalUploads = imagesToUpload.length;

    imagesToUpload.forEach((element: any, arrayIndex: number) => {
      // Find the original index in the images array
      const originalIndex = this.images.findIndex((img: any) => img === element);
      const sequence = originalIndex + 1;

      this.uploadImageFile(element.files, sequence, () => {
        completedUploads++;

        // Check if all uploads are complete
        if (completedUploads === totalUploads) {
          this.completeSubmission(responsePayload, isNewInvestment);
        }
      });
    });
  }

  private uploadImageFile(files: any[], sequence: number, onComplete: () => void): void {
    const allowedExtensions = /(\.jpg|\.jpeg|\.png)$/i;
    for (const file of files) {
      if (file) {
        if (!allowedExtensions.exec(file.name)) {
          this.toastr.danger('File type not supported', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.loading = false;
          this.submitted = false;
          onComplete(); // Call completion even on error to prevent hanging
          return;
        }
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 1024 * 10) {
          this.toastr.danger('Please ensure the file size does not exceed 10MB.', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.submitted = false;
          this.loading = false;
          onComplete(); // Call completion even on error to prevent hanging
          return;
        }
      }
    }

    const formData = new FormData();
    formData.append('DocumentType', DocumentType.Image as any);
    formData.append('Sequence', sequence as any);

    if (this.investmentId) {
      formData.append('investmentId', this.investmentId as any);
    }

    formData.append('IsInvestment', true as any);

    for (const item of files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
    }

    this.documentService
      .uploadDocument(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                this.progress = Math.round((100 * event.loaded) / event.total);
              }
              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.progress = 0;
            onComplete(); // Call completion callback on success
          }
        },
        (err: any) => {
          this.toastr.warning(err.error.message, 'Failed!');
          onComplete(); // Call completion even on error to prevent hanging
        },
      );
  }

  private completeSubmission(responsePayload: any, isNewInvestment: boolean): void {
    this.loading = false;
    this.toastr.success('Saved successfully!', 'Success');

    // For new investments, emit after all images are uploaded
    // For existing investments, emit immediately since data is already complete
    if (isNewInvestment) {
      // Wait a bit to ensure all uploads are processed on the server
      setTimeout(() => {
        this.investmentChange.emit(responsePayload);
      }, 1000);
    } else {
      this.investmentChange.emit(responsePayload);
    }

    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });
  }

  backtoList(): void {
    this.router.navigate(['/investments']);
  }

  fullImageView(src: string): void {
    this.fullImageSrc = src;
    this.display = true;
  }

  async getImage(image: string): Promise<any> {
    const blob = await this.investmentService.getImage(image);
    const url = window.URL.createObjectURL(blob);
    return this.dom.bypassSecurityTrustUrl(url);
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  onFileDropped(event: any, i: number): void {
    if (event && event.length > 1) {
      this.toastr.danger('Multiple files upload not supported, Please drag single file only', 'Upload Error!');
      return;
    }
    this.showFile(event, i);
  }

  fileBrowseHandler(event: any, i: number): void {
    this.uploadedDocuments = event.target.files;
    this.showFile(event.target.files, i);
  }

  showFile(event: any, i: number): void {
    const files = event;
    if (files.length === 0) {
      return;
    }

    const mimeType = files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      this.toastr.danger('Only images are supported.');
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(files[0]);
    reader.onload = () => {
      this.images[i].data = reader.result;
      this.images[i].files = event;
    };
  }

  // prepareFilesList(files: any[], sequence: number): void {
  //   // const extensions = ['png', 'jpeg', 'jpg', 'pdf', 'xls', 'xlsx', 'doc', 'docx', 'odt'];
  //   const allowedExtensions = /(\.jpg|\.jpeg|\.png)$/i;
  //   for (const file of files) {
  //     if (file) {
  //       if (!allowedExtensions.exec(file.name)) {
  //         this.toastr.danger('File type not supported', 'Upload Error!');
  //         this.fileDropRef.nativeElement.value = '';
  //         this.loading = false;
  //         this.submitted = false;
  //         return;
  //       }
  //       const fsize = file.size;
  //       const fileSize = Math.round(fsize / 1024);
  //       if (fileSize >= 1024 * 10) {
  //         this.toastr.danger('Please ensure the file size does not exceed 10MB.', 'Upload Error!');
  //         this.fileDropRef.nativeElement.value = '';
  //         this.submitted = false;
  //         this.loading = false;
  //         return;
  //       }
  //     }
  //   }

  //   const formData = new FormData();

  //   formData.append('DocumentType', DocumentType.Image as any);
  //   formData.append('Sequence', sequence as any);

  //   if (this.investmentId) {
  //     formData.append('investmentId', this.investmentId as any);
  //   }

  //   formData.append('IsInvestment', true as any);

  //   for (const item of files) {
  //     item.progress = 0;
  //     formData.append('Files', item, item.name);
  //   }

  //   this.documentService
  //     .uploadDocument(formData)
  //     .pipe(
  //       map((event: any) => {
  //         switch (event.type) {
  //           case HttpEventType.UploadProgress:
  //             if (event.total) {
  //               this.progress = Math.round((100 * event.loaded) / event.total);
  //             }
  //             return { status: 'progress', message: this.progress };

  //           case HttpEventType.Response:
  //             return event.body;
  //           default:
  //             return `Unhandled event: ${event.type}`;
  //         }
  //       }),
  //     )
  //     .subscribe(
  //       (res: any) => {
  //         if (res.success) {
  //           // this.toastr.success('File Uploaded Successfully!', 'Success!');
  //           this.progress = 0;
  //         }
  //       },
  //       (err: any) => this.toastr.success(err.error.message, 'Failed!'),
  //     );
  // }

  deleteDocument(documentKey: string, index: number): void {
    if (documentKey) {
      this.dialogService
        .open(DeleteDocumentComponent, {
          context: {
            documentKey,
          },
          autoFocus: false,
        })
        .onClose.subscribe((res: any) => {
          if (res) {
            this.images[index].data = undefined;
          }
        });
    } else {
      this.images[index].data = undefined;
    }
  }

  private loadInvestmentData(details: InvestmentDetailsResponse): void {
    // Extract investment ID (adjust property name as needed)
    this.investmentId = details.id || 0;

    // Load overview data
    this.editorData = details.overview || '';
    this.overviewForm.patchValue({
      overview: this.editorData,
    });

    // Load template ID
    this.templateId = details.templateId || 1;

    // Handle images
    if (details.imageUrls && details.imageUrls.length > 0) {
      this.handleImageUrls(details.imageUrls);
    } else {
      this.initializeEmptyImages();
    }

    // Store the full investment data
    this.investments = details;

    // Reconfigure editor with new investment ID
    this.setConfig();
  }

  private initializeEmptyImages(): void {
    for (const index in this.imagesCount) {
      if (index) {
        if (this.imagesCount.length - 1 === Number(index)) {
          return;
        }
        if (!this.images[index]) {
          this.images[index] = {};
        }
        this.images[index] = { data: null };
      }
    }
  }

  private handleImageUrls(imageUrls: any[]): void {
    for (const index in this.imagesCount) {
      if (index) {
        if (this.imagesCount.length - 1 === Number(index)) {
          return;
        }

        if (!this.images[index]) {
          this.images[index] = { loading: true };
        }

        const [imageData] = imageUrls.filter((t: any) => Number(t.sequence) === Number(index) + 1);
        if (imageData) {
          this.images[index] = {
            loading: false,
            data: imageData.url || ImageHelper.noImage(),
            documentKey: imageData.documentKey,
          };
        } else {
          this.images[index] = { loading: false, data: null };
        }
      }
    }
  }

  onSearchChange(searchTerm: string): void {
    if (!searchTerm || searchTerm.trim().length < 2) {
      this.searchResults = [];
      this.isLoading = false;
      return;
    }

    this.isLoading = true;
    this.getInvestmentsLookupByTMOLoan(searchTerm.trim()).subscribe({
      next: (results) => {
        this.searchResults = results;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Search error:', error);
        this.searchResults = [];
        this.isLoading = false;
      },
    });
  }

  onSelectionChange(selectedItem: InvestmentsLookupByTMOLoanResponse | null): void {
    // Handle selection change
    if (selectedItem) {
      this.loadInvestmentDetailsTMO(selectedItem.externalId);
    }
  }

  private loadInvestmentDetailsTMO(externalId: string): void {
    this.investmentService.getDetailsLookupWithTMOLoanId(externalId).subscribe({
      next: (response: HttpResponse<InvestmentDetailsTMOLoanResponse>) => {
        if (response.success && response.payload) {
          this.detailsTMOSelected = response.payload;
          // this.editorData = this.detailsTMOSelected.overview || '';
          // this.overviewForm.patchValue({
          //   overview: this.editorData,
          // });
        } else {
          this.toastr.danger(response.error.message, 'Error!');
          this.detailsTMOSelected = null;
        }
      },
      error: (error) => {
        console.error('Error loading investment details TMO:', error);
        this.detailsTMOSelected = null;
      },
    });
  }

  private getInvestmentsLookupByTMOLoan(searchTerm: string): Observable<InvestmentsLookupByTMOLoanResponse[]> {
    const params: SearchParam = {
      search: searchTerm,
      pageNumber: 0, // Zero-based index as specified
      pageSize: 10,
    };

    return this.investmentService.getInvestmentsLookupByTMOLoan(params).pipe(
      switchMap((response: HttpResponse<InvestmentsLookupPayload>) => {
        if (response.success && response.payload && response.payload.investments) {
          return of(response.payload.investments);
        }
        return of([]);
      }),
      catchError((error) => {
        console.error('API Error:', error);
        return of([]);
      }),
    );
  }

  onClearTMOSelection(): void {
    this.detailsTMOSelected = null;
  }
}
