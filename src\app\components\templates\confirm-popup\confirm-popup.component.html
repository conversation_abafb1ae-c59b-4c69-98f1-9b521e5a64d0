<nb-card>
  <nb-card-header>
    <div class="flex flex-wrap -mx-2" *ngIf="yesSuccessPopUp">
      <h5 style="margin: auto" class="w-10/12 px-2">{{ title }}</h5>
      <div class="w-2/12 px-2">
        <div class="popup-close float-right">
          <button ghost nbButton (click)="close(false)">
            <nb-icon icon="close"></nb-icon>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>

  <nb-card-body class="" style="width: 687px">
    <nb-icon style="font-size: 102px; margin: auto; display: block; color: #4edaa7" *ngIf="icon" [icon]="icon">
    </nb-icon>

    <div class="text-center" style="margin: 5%">
      <p>{{ message }}</p>
      <br />

      <div class="text-center">
        <button class="button mr-2" (click)="close(false)" nbButton size="large" status="basic" *ngIf="showNoButton">
          {{ noButton }}
        </button>
        <button class="button" (click)="close(true)" nbButton size="large" status="primary">
          <nb-icon *ngIf="yesButtonIcon" [icon]="yesButtonIcon" [pack]="yesButtonIconPack"></nb-icon>
          {{ yesButton }}
        </button>
      </div>
    </div>
  </nb-card-body>
</nb-card>
